module.exports = () => {
  const glob = require('glob');
  const fs = require('fs');
  const YAML = require('yamljs');

  // Definição das layers que queremos adicionar a todas as funções
  const commonLayers = [
    { Ref: 'AwsCoreLambdaLayer' },
    { Ref: 'UtilsLambdaLayer' },
  ];

  const files = glob.sync('./handlers/**/**.yml');
  const merged = files
    .map((file) => fs.readFileSync(`${file}`, 'utf8'))
    .map((raw) => YAML.parse(raw))
    .reduce((result, handler) => Object.assign(result, handler), {});

  console.info('Merging files:');
  console.info(files);

  // Adicionar layers a todas as funções
  Object.keys(merged).forEach((functionName) => {
    const functionConfig = merged[functionName];

    // Se a função já tem layers definidas
    if (functionConfig.layers) {
      functionConfig.layers = functionConfig.layers
        .filter(layer => layer !== null)
        .map(layer => {
          // Se já está no formato correto (objeto Ref), mantém
          if (typeof layer === 'object' && layer.Ref) {
            return layer;
          }

          // Se é string, assume que é nome da layer e converte para { Ref }
          if (typeof layer === 'string') {
            return { Ref: layer };
          }

          // Caso raro — retorna como está
          return layer;
        });

      // Adiciona apenas layers que ainda não existem
      commonLayers.forEach(layer => {
        const alreadyExists = functionConfig.layers.some(existingLayer =>
          existingLayer.Ref === layer.Ref
        );
        if (!alreadyExists) {
          functionConfig.layers.push(layer);
        }
      });
    } else {
      // Se não tinha layers, define todas as comuns
      functionConfig.layers = [...commonLayers];
    }
  });

  return merged;
};
