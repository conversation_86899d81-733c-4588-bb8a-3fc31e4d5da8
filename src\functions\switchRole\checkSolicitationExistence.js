import { message, json } from "../../shared/response";
import {
  checkSwitchRoleSolicitation,
  readSwitchRoleSolicitations,
} from "../../model/dynamo";
import { parsePath } from "../../shared/parsers";
import { DatabaseEntity } from "../../entities/database-entity";

async function sendDataToUser(status, statusString, data) {
  const msg = await message(statusString, data);
  return await json(msg, status);
}

exports.handler = async (event) => {
  const { user, account } = parsePath(event);
  const databaseEntity = new DatabaseEntity();
  console.log(user);
  const readByUserInput = databaseEntity.generateReadSwitchRoleByUserInput({
    user,
    account,
  });

  try {
    return await sendDataToUser(
      200,
      "success",
      await checkSwitchRoleSolicitation(readByUserInput)
    );
  } catch (error) {
    console.log(error);
    return await sendDataToUser(500, "error", error);
  }
};
