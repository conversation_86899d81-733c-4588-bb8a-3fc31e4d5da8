import { message, json } from "../../shared/response";
import { readSwitchRoleSolicitations } from "../../model/dynamo";
import { parsePath } from "../../shared/parsers";

async function sendDataToUser(status, statusString, data) {
  const msg = await message(statusString, data);
  return await json(msg, status);
}

exports.handler = async (event) => {
  try {
    return await sendDataToUser(
      200,
      "success",
      await readSwitchRoleSolicitations()
    );
  } catch (error) {
    console.log(error);
    return await sendDataToUser(500, "error", error);
  }
};
