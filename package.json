{"name": "dynamodb-back-end", "version": "1.0.0", "description": "API de comunicação com o DynamoDB", "main": "index.js", "scripts": {"prepare": "husky install", "test": "npx jest --only<PERSON><PERSON>ed", "local": "npx serverless offline --stage dev ", "deploy": "npx serverless deploy --stage $STAGE --force --verbose"}, "author": "Dosystems", "license": "ISC", "devDependencies": {"@babel/core": "^7.12.10", "@babel/preset-env": "^7.12.11", "@types/jest": "^26.0.22", "@types/uuid": "^8.3.0", "athena-express": "^7.1.5", "babel-loader": "^8.2.2", "corejs": "^1.0.0", "glob": "^7.2.0", "husky": "^8.0.3", "jest": "^29.0.0", "jest-extended": "^0.11.5", "noop2": "^2.0.0", "serverless": "^3.40.0", "serverless-layers": "2.5.1", "serverless-offline": "^12.0.4", "serverless-plugin-split-stacks": "^1.14.0", "serverless-webpack": "^5.15.1", "webpack": "^5.19.0", "webpack-node-externals": "^3.0.0", "webpackbar": "^7.0.0"}, "dependencies": {"athena-express": "^7.1.5", "aws-multipart-parser": "^0.2.1", "aws-sdk": "^2.839.0", "axios": "^0.21.4", "date-fns": "^2.29.1", "dotenv": "^10.0.0", "excel4node": "^1.8.0", "nodemailer": "^6.7.8", "pdfmake": "^0.2.6", "serverless-apigw-binary": "^0.4.4", "uuid": "^8.3.2", "yamljs": "^0.3.0", "yup": "^0.32.11"}}