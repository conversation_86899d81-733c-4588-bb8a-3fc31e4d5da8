import { SecretsManager } from 'aws-sdk'

export const makeSecretsManager = (params = null) =>
    new SecretsManager(params || { region: process.env.AWS_REGION_LOCATION })


const secreatsManager = makeSecretsManager()
export async function getWafToken() {
    const token = await secreatsManager.getSecretValue({
      SecretId: process.env.DSM_API_WAF_ARN,
    })
    .promise();

    return JSON.parse(token.SecretString).token
}