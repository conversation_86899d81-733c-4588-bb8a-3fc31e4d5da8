cognito-read:
  handler: src/functions/cognito/read.handler
  name: ${self:custom.dotenv.STAGE}-cognito-read${self:custom.dotenv.VERSION}
  description: Função para leitura de usuários do Cognito
  memorySize: 128
  events:
    - http:
        path: /cognito/read
        method: get
        cors: true
        authorizer: ${self:custom.authorizer}

update-status:
  handler: src/functions/cognito/updateStatus.handler
  name: ${self:custom.dotenv.STAGE}-update-status${self:custom.dotenv.VERSION}
  description: Função para atualização do status do usuário
  memorySize: 128
  events:
    - http:
        path: /cognito/update/status
        method: put
        cors: true
        authorizer: ${self:custom.authorizer}

update-role:
  handler: src/functions/cognito/updateRole.handler
  name: ${self:custom.dotenv.STAGE}-update-role${self:custom.dotenv.VERSION}
  description: Função para atualização da role do usuário
  memorySize: 128
  events:
    - http:
        path: /cognito/update/role
        method: put
        cors: true
        authorizer: ${self:custom.authorizer}

token:
  handler: src/functions/cognito/token.handler
  name: ${self:custom.dotenv.STAGE}-token${self:custom.dotenv.VERSION}
  description: Função para obter um token do cognito
  memorySize: 128
  events:
    - http:
        path: /cognito/token
        method: post
        cors: true
        # authorizer:
        #   name: ${self:custom.dotenv.AWS_API_GATEWAY_COGNITO_NAME}
        #   arn: arn:aws:cognito-idp:${self:custom.dotenv.AWS_REGION_LOCATION}:${self:custom.dotenv.ACCOUNT_ID}:userpool/${self:custom.dotenv.USER_POOL_ID}

access-token:
  handler: src/functions/cognito/accessToken.handler
  name: ${self:custom.dotenv.STAGE}-access-token${self:custom.dotenv.VERSION}
  description: Função para obter um token do OTRS do cognito
  memorySize: 128
  events:
    - http:
        path: /cognito/access-token
        method: get
        cors:
          origin: "*"
          headers: "*"
        authorizer: ${self:custom.authorizer}
