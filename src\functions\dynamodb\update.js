import { parseBody, parseHeaders, parsePath } from "../../shared/parsers";
import { message, json } from "../../shared/response";
import { put } from "../../model/dynamo";
import { StepFunctions } from "aws-sdk";

const stepFunctions = new StepFunctions({
  region: process.env.AWS_REGION_LOCATION,
});

async function sendDataToUser(status, statusString, data) {
  const msg = await message(statusString, data);
  return await json(msg, status);
}

export const handler = async (event, context) => {
  try {
    console.log({ event });
    const url = parsePath(event);
    const body = parseBody(event);
    const head = parseHeaders(event);

    const { id } = url;
    const { dynamodb } = head;

    const upObjDynamo = await put(dynamodb, id, body);

    const notifyTables = process.env.TABLES_TO_SEND_EVENT
      ? process.env.TABLES_TO_SEND_EVENT.split(";")
      : [];
    if (notifyTables.includes(dynamodb)) {
      try {
        console.log(
          `Sending register event - table: ${dynamodb} - id: ${body.id}`
        );
        await stepFunctions
          .startExecution({
            stateMachineArn: process.env.CREATE_EVENT_STEP_FUNCTION,
            input: JSON.stringify({
              eventType: "UPDATE",
              tableName: dynamodb,
              item: body,
            }),
          })
          .promise();
      } catch (error) {
        console.log(
          `Could not send register event - table: ${dynamodb} - id: ${body.id}`
        );
        console.log(error);
      }
    }

    return await sendDataToUser(200, "success", upObjDynamo);
  } catch (error) {
    console.log(error);
    return await sendDataToUser(500, "error", error);
  }
};
