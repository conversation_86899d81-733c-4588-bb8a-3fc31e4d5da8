import { message, json } from '../../shared/response'
import { CognitoIdentityServiceProvider } from 'aws-sdk'

const cognito = new CognitoIdentityServiceProvider({
  region: process.env.AWS_REGION_LOCATION
})

async function sendDataToUser (status, statusString, data) {
  const msg = await message(statusString, data)
  return await json(msg, status)
}

export const handler = async (event, context) => {
  try {
    const read = await cognito.listUsers({
      UserPoolId: process.env.USER_POOL_ID
    }).promise()

    while (read.PaginationToken) {
      const { Users, PaginationToken } = await cognito.listUsers({
        UserPoolId: process.env.USER_POOL_ID,
        PaginationToken: read.PaginationToken
      }).promise()

      read.Users.push(...Users)

      read.PaginationToken = PaginationToken
    }

    const response = read.Users.map(user => {
      const obj = {}

      user.Attributes.map(attribute => {
        const refactorName = {
          email: () => obj.email = attribute.Value,
          role: () => obj.permission = attribute.Value,
          hml_role: () => obj.hml_permission = attribute.Value,
          dev_role: () => obj.dev_permission = attribute.Value
        }

        try {
          refactorName[attribute?.Name?.replace('custom:', '')]()
        } catch (err) {
          // console.log(err)
        }

        if (user.Username.includes('\\')) obj.user = user.Username.split('\\')[1]
        else obj.user = user.Username
        obj.status = user.Enabled

        return attribute
      })

      return obj
    })

    console.log(read.Users)

    return await sendDataToUser(200, 'success', response.filter(e => e?.user !== undefined))
  } catch (error) {
    console.log(error)
    return await sendDataToUser(500, 'error', error)
  }
}
