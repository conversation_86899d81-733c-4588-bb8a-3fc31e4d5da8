get-invoices:
  handler: src/functions/invoices/get-invoices.handler
  name: ${self:custom.dotenv.STAGE}-get-invoices${self:custom.dotenv.VERSION}
  description: Função para buscar os invoices dos clientes em um periodo de data
  memorySize: 128
  timeout: 120
  events:
    - schedule:
        rate: rate(1 day)
        name: ${self:custom.dotenv.STAGE}-get-invoices-schedule${self:custom.dotenv.VERSION}
    - http:
        path: /invoices
        method: post
        cors: true
        authorizer: ${self:custom.authorizer}

read-invoices:
  handler: src/functions/invoices/read-invoices.handler
  name: ${self:custom.dotenv.STAGE}-read-invoices${self:custom.dotenv.VERSION}
  description: Função para buscar os invoices dos clientes em um periodo de data
  memorySize: 128
  timeout: 120
  events:
    - http:
        path: /read-invoices
        method: get
        cors: true
        authorizer: ${self:custom.authorizer}
