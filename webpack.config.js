const slsw = require('serverless-webpack');
const nodeExternals = require('webpack-node-externals');
const WebpackBar = require('webpackbar');

module.exports = {
  entry: slsw.lib.entries,
  target: 'node',
  mode: slsw.lib.webpack.isLocal ? 'development' : 'production',
  optimization: {
    minimize: false
  },
  devtool: slsw.lib.webpack.isLocal ? 'inline-cheap-module-source-map' : 'source-map',
  externals: [nodeExternals()],
  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: [
              [
                '@babel/preset-env',
                {
                  targets: { node: '18' },
                  useBuiltIns: 'usage',
                  corejs: 3
                }
              ]
            ]
          }
        }
      }
    ]
  },
  plugins: [
    new WebpackBar({
      reporters: ['basic'] 
    })
  ],
  infrastructureLogging: {
    level: 'error'
  }
};