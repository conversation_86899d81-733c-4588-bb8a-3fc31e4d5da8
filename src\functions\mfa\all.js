// Importando módulos
import { SecretsManager } from 'aws-sdk'

// Importando arquivos
import { parseBody } from '../../shared/parsers'
import { json, message } from '../../shared/response'

const cli = new SecretsManager({
  region: process.env.AWS_REGION_LOCATION
})

async function sendDataToUser (status, statusString, data) {
  const msg = await message(statusString, data)
  return await json(msg, status)
}

// Exportando handler
export const handler = async (event, context) => {
  // Capturando os dados do body da requisição
  const body = parseBody(event)

  try {
    // Lendo as secrets
    const all = await cli.getSecretValue({
      SecretId: process.env.AWS_SECRET_GOOGLE_AUTH_TOKEN
    }).promise()

    // Parseando secret
    const params = JSON.parse(all.SecretString)

    if (body.only_read == 0) {
      // Adicionando chave MFA no secret
      params[body.user] = body.secret

      // Armazenando token no Secrets Manager
      const secret = await cli.putSecretValue({
        SecretId: process.env.AWS_SECRET_GOOGLE_AUTH_TOKEN,
        SecretString: JSON.stringify(params)
      }).promise()

      console.log(secret)
    } else if (body.only_read == 2) {
      // Removendo chave MFA no secret
      delete params[body.user]

      // Armazenando token no Secrets Manager
      const secret = await cli.putSecretValue({
        SecretId: process.env.AWS_SECRET_GOOGLE_AUTH_TOKEN,
        SecretString: JSON.stringify(params)
      }).promise()

      console.log(secret)
    }

    // Criando mensagem de sucesso e passando os dados
    return await sendDataToUser(200, 'success', params)
  } catch (error) {
    // Definindo mensagem de erro
    return await sendDataToUser(200, 'success', error)
  }
}
