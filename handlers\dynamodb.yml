create-data:
  handler: src/functions/dynamodb/create.handler
  name: ${self:custom.dotenv.STAGE}-create-data${self:custom.dotenv.VERSION}
  description: Função para inserção de um dado em uma tabela do DynamoDB
  memorySize: 128
  events:
    - http:
        path: /create
        method: post
        cors:
          origin: "*"
          headers:
            - DynamoDB
            - Content-Type
            - X-Amz-Date
            - Authorization
            - X-Api-Key
            - X-Amz-Security-Token
        authorizer: ${self:custom.authorizer}

read-data:
  handler: src/functions/dynamodb/read.handler
  name: ${self:custom.dotenv.STAGE}-read-data${self:custom.dotenv.VERSION}
  description: Função para leitura de dados da tabela do DynamoDB
  memorySize: 128
  events:
    - http:
        path: /read/{form}/{id}
        method: get
        cors:
          origin: "*"
          headers:
            - DynamoDB
            - Content-Type
            - X-Amz-Date
            - Authorization
            - X-Api-Key
            - X-Amz-Security-Token
            - x-waf-header
        authorizer: ${self:custom.authorizer}

read-audits:
  handler: src/functions/dynamodb/readAudits.handler
  name: ${self:custom.dotenv.STAGE}-read-audits${self:custom.dotenv.VERSION}
  description: Função para leitura de dados de auditoria da tabela do DynamoDB
  memorySize: 128
  events:
    - http:
        path: /read/audits/{month}/{year}
        method: get
        cors:
          origin: "*"
          headers:
            - DynamoDB
            - Content-Type
            - X-Amz-Date
            - Authorization
            - X-Api-Key
            - X-Amz-Security-Token
        authorizer: ${self:custom.authorizer}

read-audits-switch-role:
  handler: src/functions/dynamodb/readAuditsSwitchRole.handler
  name: ${self:custom.dotenv.STAGE}-read-audits-switch-role${self:custom.dotenv.VERSION}
  description: Função para leitura de dados de switch role de auditoria da tabela do DynamoDB
  memorySize: 128
  events:
    - http:
        path: /read/audits
        method: get
        cors:
          origin: "*"
          headers:
            - DynamoDB
            - Content-Type
            - X-Amz-Date
            - Authorization
            - X-Api-Key
            - X-Amz-Security-Token
        authorizer: ${self:custom.authorizer}

update-data:
  handler: src/functions/dynamodb/update.handler
  name: ${self:custom.dotenv.STAGE}-update-data${self:custom.dotenv.VERSION}
  description: Função para atualização de um dado na tabela do DynamoDB
  memorySize: 128
  events:
    - http:
        path: /update/{id}
        method: put
        cors:
          origin: "*"
          headers:
            - DynamoDB
            - Content-Type
            - X-Amz-Date
            - Authorization
            - X-Api-Key
            - X-Amz-Security-Token
        authorizer: ${self:custom.authorizer}

delete-data:
  handler: src/functions/dynamodb/delete.handler
  name: ${self:custom.dotenv.STAGE}-delete-data${self:custom.dotenv.VERSION}
  description: Função para deleção de um dado na tabela do DynamoDB
  memorySize: 128
  events:
    - http:
        path: /delete/{id}
        method: delete
        cors:
          origin: "*"
          headers:
            - DynamoDB
            - Content-Type
            - X-Amz-Date
            - Authorization
            - X-Api-Key
            - X-Amz-Security-Token
        authorizer: ${self:custom.authorizer}

get-paginate:
  handler: src/functions/dynamodb/read-paginate.handler
  name: ${self:custom.dotenv.STAGE}-read-paginate${self:custom.dotenv.VERSION}
  description: Função para buscar as registros paginados no Dynamodb
  memorySize: 128
  timeout: 120
  events:
    - http:
        path: /read/paginate
        method: get
        cors:
          origin: "*"
          headers:
            - DynamoDB
            - Content-Type
            - X-Amz-Date
            - Authorization
            - X-Api-Key
            - X-Amz-Security-Token
        authorizer: ${self:custom.authorizer}
