invoke-state-machine:
  handler: src/functions/switchRole/invokeStateMachine.handler
  name: ${self:custom.dotenv.STAGE}-invoke-state-machine${self:custom.dotenv.VERSION}
  description: Função para chamar a Step Function que contém a State Machine para troca de role
  memorySize: 128
  events:
    - http:
        path: /state-machine
        method: post
        cors:
          origin: "*"
          headers:
            - Content-Type
            - X-Amz-Date
            - Authorization
            - X-Api-Key
            - X-Amz-Security-Token
            - x-waf-header
        authorizer: ${self:custom.authorizer}

check-darede-full-role:
  handler: src/functions/switchRole/checkDaredeFullRole.handler
  name: ${self:custom.dotenv.STAGE}-check-darede-full-role
  description: Função para criar role na conta do cliente
  memorySize: 128
  timeout: 900
  events:
    - http:
        path: /check-darede-full-role
        method: post
        cors:
          origin: "*"
          headers:
            - Content-Type
            - X-Amz-Date
            - Authorization
            - X-Api-Key
            - X-Amz-Security-Token
            - x-waf-header
        authorizer:
          name: ${self:custom.dotenv.AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${self:custom.dotenv.AWS_REGION_LOCATION}:${self:custom.dotenv.ACCOUNT_ID}:userpool/${self:custom.dotenv.USER_POOL_ID}

read-switch-role-solicitations:
  handler: src/functions/switchRole/readSwitchRoleData.handler
  name: ${self:custom.dotenv.STAGE}-read-switch-role-data${self:custom.dotenv.VERSION}
  description: Função para leitura de dados de switch role da tabela do DynamoDB
  memorySize: 128
  events:
    - http:
        path: /read/switch-role
        method: get
        cors:
          origin: "*"
          headers:
            - DynamoDB
            - Content-Type
            - X-Amz-Date
            - Authorization
            - X-Api-Key
            - X-Amz-Security-Token
        authorizer: ${self:custom.authorizer}

check-solicitation-existence:
  handler: src/functions/switchRole/checkSolicitationExistence.handler
  name: ${self:custom.dotenv.STAGE}-check-solicitation-existence${self:custom.dotenv.VERSION}
  description: Função para checar se existe uma solicitação de switch role para o usuário e conta informados
  memorySize: 128
  events:
    - http:
        path: /read/switch-role-exists/{user}/{account}
        method: get
        cors:
          origin: "*"
          headers:
            - DynamoDB
            - Content-Type
            - X-Amz-Date
            - Authorization
            - X-Api-Key
            - X-Amz-Security-Token
        authorizer: ${self:custom.authorizer}

check-if-role-admin-exists:
  handler: src/functions/switchRole/checkIfRoleAdminExists.handler
  name: ${self:custom.dotenv.STAGE}-check-if-role-admin-exists
  description: Função para checar e retentar criar switch-role em caso de falha
  memorySize: 128
  timeout: 300
  events:
    - schedule:
        rate: cron(0 3 * * ? *)
    - enabled: true
    - http:
        path: /check-if-role-admin-exists
        method: get
        cors:
          origin: "*"
          headers:
            - Content-Type
            - X-Amz-Date
            - Authorization
            - X-Api-Key
            - X-Amz-Security-Token
            - x-waf-header
        authorizer:
          name: ${self:custom.dotenv.AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${self:custom.dotenv.AWS_REGION_LOCATION}:${self:custom.dotenv.ACCOUNT_ID}:userpool/${self:custom.dotenv.USER_POOL_ID}
