import { message, json } from "../../shared/response";
import { readAudits } from "../../model/dynamo";
import { parsePath, parseQueryString } from "../../shared/parsers";

async function sendDataToUser(status, statusString, data) {
  const msg = await message(statusString, data);
  return await json(msg, status);
}

export const handler = async (event, context) => {
  console.log({ event });
  const { month, year } = parsePath(event);
  const { nextPage } = parseQueryString(event);

  try {
    const { audits, lastEvaluatedKey } = await readAudits({
      month,
      year,
      nextPage,
    });

    return await sendDataToUser(200, "success", {
      audits,
      nextPage: lastEvaluatedKey ? lastEvaluatedKey.id : "",
    });
  } catch (error) {
    console.log(error);
    return await sendDataToUser(500, "error", error);
  }
};
