create-dolar:
  handler: src/functions/dolar/create.handler
  name: ${self:custom.dotenv.STAGE}-create-dolar${self:custom.dotenv.VERSION}
  description: Função para registrar o valor do dolar no DynamoDB
  memorySize: 128
  events:
    - http:
        path: /create/dolar
        method: post
        cors: true
        authorizer: ${self:custom.authorizer}

read-dolar:
  handler: src/functions/dolar/read.handler
  name: ${self:custom.dotenv.STAGE}-read-dolar${self:custom.dotenv.VERSION}
  description: Função para leitura do dolar da tabela do DynamoDB
  memorySize: 128
  events:
    - http:
        path: /read/dolar/{month}/{year}
        method: get
        cors: true
        authorizer: ${self:custom.authorizer}
