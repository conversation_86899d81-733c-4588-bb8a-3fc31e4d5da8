read-customer:
  handler: src/functions/customers/readCustomers.handler
  name: ${self:custom.dotenv.STAGE}-read-customer${self:custom.dotenv.VERSION}
  description: Função para retornar um cliente com base no status
  memorySize: 128
  timeout: 900
  events:
    - http:
        path: /customers/read/{form}
        method: get
        cors:
          origin: "*"
          headers:
            - DynamoDB
            - Content-Type
            - X-Amz-Date
            - Authorization
            - X-Api-Key
            - X-Amz-Security-Token
        authorizer: ${self:custom.authorizer}

update-has-active-contracts:
  handler: src/functions/customers/updateHasActiveContracts.handler
  name: ${self:custom.dotenv.STAGE}-update-has-active-contracts${self:custom.dotenv.VERSION}
  description: Função para retornar um cliente com base no status
  memorySize: 128
  timeout: 900
  events:
    - http:
        path: /customers/updateHasActiveContracts/{customerId}
        method: put
        cors:
          origin: "*"
          headers:
            - DynamoDB
            - Content-Type
            - X-Amz-Date
            - Authorization
            - X-Api-Key
            - X-Amz-Security-Token
        authorizer: ${self:custom.authorizer}

read-customerV2:
  handler: src/functions/customers/getCustomers.handler
  name: ${self:custom.dotenv.STAGE}-read-customers${self:custom.dotenv.VERSION}
  description: Função para leitura de dados de customers da tabela do DynamoDB
  memorySize: 128
  events:
    - http:
        path: /read/customers
        method: get
        cors:
          origin: "*"
          headers:
            - DynamoDB
            - Content-Type
            - X-Amz-Date
            - Authorization
            - X-Api-Key
            - X-Amz-Security-Token
        authorizer: ${self:custom.authorizer}
