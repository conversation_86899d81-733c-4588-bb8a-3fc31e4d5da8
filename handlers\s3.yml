generate-link:
  handler: src/functions/s3/linkGenerator.handler
  name: ${self:custom.dotenv.STAGE}-generate-link${self:custom.dotenv.VERSION}
  description: Função para geração de link para ações em objetos no S3
  memorySize: 128
  events:
    - http:
        path: /s3/link
        method: post
        cors: true
        authorizer: ${self:custom.authorizer}

upload-obj-to-bucket:
  handler: src/functions/s3/uploadObjToBucket.handler
  name: ${self:custom.dotenv.STAGE}-upload-obj-to-bucket${self:custom.dotenv.VERSION}
  description: Função para fazer upload de objetos para um determinado bucket
  memorySize: 128
  events:
    - http:
        path: /upload/object
        method: post
        cors: true
        authorizer: ${self:custom.authorizer}

copy-obj-to-another-bucket:
  handler: src/functions/s3/copyObjToAnotherBucket.handler
  name: ${self:custom.dotenv.STAGE}-copy-obj-to-another-bucket${self:custom.dotenv.VERSION}
  description: Função para fazer a cópia de objetos para um determinado bucket
  memorySize: 128
  events:
    - http:
        path: /copy/object
        method: post
        cors: true
        authorizer: ${self:custom.authorizer}

delete-multiple-objects:
  handler: src/functions/s3/deleteMultipleObjects.handler
  name: ${self:custom.dotenv.STAGE}-delete-multiple-objects${self:custom.dotenv.VERSION}
  description: Função para fazer a deleção de objetos de um determinado bucket
  memorySize: 128
  events:
    - http:
        path: /delete/objects
        method: post
        cors: true
        authorizer: ${self:custom.authorizer}

list-bucket-objects:
  handler: src/functions/s3/listObjectsInBucket.handler
  name: ${self:custom.dotenv.STAGE}-list-bucket-objects${self:custom.dotenv.VERSION}
  description: Função para fazer a listagem de objetos de um determinado bucket
  memorySize: 128
  events:
    - http:
        path: /list/objects
        method: post
        cors: true
        authorizer: ${self:custom.authorizer}
