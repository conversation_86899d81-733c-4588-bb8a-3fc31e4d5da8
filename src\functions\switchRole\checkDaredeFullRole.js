import { checkDaredeFullRole } from "../../shared/checkDaredeFullRole";

exports.handler = async (event) => {
  try {
    const body = JSON.parse(event.body);
    const account = body.account;

    console.log("body: ", body.account, event.body);

    const result = await checkDaredeFullRole(account);

    console.log("Result:", result);

    return {
      statusCode: 200,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
      body: JSON.stringify(result),
    };
  } catch (error) {
    console.error("Erro capturado na Lambda:", error);
    return {
      statusCode: 500,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
      body: JSON.stringify({ error: error.message || "Internal Server Error" }),
    };
  }
};
