"use strict";

export const STATUS_CODE = {
  SUCCESS: 200,
  BAD_REQUEST: 400,
  NOT_FOUND: 404,
  CONFLICT: 409,
  ERROR: 500,
};

export async function message(status, data) {
  return {
    status,
    data,
  };
}

export async function json(body = {}, status = 200) {
  return {
    statusCode: status,
    headers: {
      "Access-Control-Allow-Origin": "*",
    },
    body: body != null ? JSON.stringify(body.stack ? body.stack : body) : "",
  };
}

export const responseWithError = (message) => {
  return {
    statusCode: STATUS_CODE.ERROR,
    headers: {
      "Access-Control-Allow-Origin": "*",
    },
    body: JSON.stringify({
      message,
    }),
  };
};

export const responseWithBadRequest = (message) => {
  return {
    statusCode: STATUS_CODE.BAD_REQUEST,
    headers: {
      "Access-Control-Allow-Origin": "*",
    },
    body: JSON.stringify({
      message,
    }),
  };
};

export const responseWithNotFound = (message) => {
  return {
    statusCode: STATUS_CODE.NOT_FOUND,
    headers: {
      "Access-Control-Allow-Origin": "*",
    },
    body: JSON.stringify({
      message,
    }),
  };
};

export const responseWithConflict = (message) => {
  return {
    statusCode: STATUS_CODE.CONFLICT,
    headers: {
      "Access-Control-Allow-Origin": "*",
    },
    body: JSON.stringify({
      message,
    }),
  };
};

export const responseWithSuccess = (data, message = "") => {
  return {
    statusCode: STATUS_CODE.SUCCESS,
    headers: {
      "Access-Control-Allow-Origin": "*",
    },
    body: JSON.stringify(data || message),
  };
};
