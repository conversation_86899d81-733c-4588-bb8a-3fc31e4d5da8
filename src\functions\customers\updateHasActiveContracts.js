import { message, json } from "../../shared/response";
import { ContractEntity } from "../../entities/contract-entity";
import { CustomerEntity } from "../../entities/customer-entity";
import { makeDynamoDB } from "../../shared/services/dynamo-service";
import { parsePath } from "../../shared/parsers";
import { DatabaseEntity } from "../../entities/database-entity";
async function sendDataToUser(status, statusString, data) {
  const msg = await message(statusString, data);
  return await json(msg, status);
}

const clientDynamo = makeDynamoDB();

export const handler = async (event) => {
  const databaseEntity = new DatabaseEntity(clientDynamo);
  const contractsEntity = new ContractEntity(clientDynamo);
  let customerId = "";

  try {
    const body = JSON.parse(event.Records[0].Sns.Message);
    console.log(JSON.stringify(body));
    if (body.eventType === "CREATE") customerId = body.item.customer_id;
    else customerId = body.item.customer.Value.id;
  } catch (e) {
    customerId = parsePath(event);
    customerId = customerId.customerId;
  }
  const inputGetContracts = contractsEntity.generateInputGetContracts({
    customer_id: customerId,
  });

  try {
    const customerContracts = await contractsEntity.getContracts(
      inputGetContracts
    );
    if (customerContracts.length === 0) {
      const inputUpdateItem = databaseEntity.generateUpdateInput({
        id: customerId,
        data: { has_active_contracts: 2 },
        table: `${process.env.FINOPS_STAGE}-customers`,
      });
      const updateItemResponse = await databaseEntity.updateItem(
        inputUpdateItem
      );
      console.log("Cliente: ", customerId, "não tem contratos");
      return await sendDataToUser(200, "success", updateItemResponse);
    } else {
      let checkContractsStatus = customerContracts.some(
        (contract) => contract.active === 1
      );
      if (checkContractsStatus === true) {
        const inputUpdateItem = databaseEntity.generateUpdateInput({
          id: customerId,
          data: { has_active_contracts: 1 },
          table: `${process.env.FINOPS_STAGE}-customers`,
        });
        const updateItemResponse = await databaseEntity.updateItem(
          inputUpdateItem
        );
        console.log("Cliente: ", customerId, "tem contratos ativos");
        return await sendDataToUser(200, "success", updateItemResponse);
      } else {
        const inputUpdateItem = databaseEntity.generateUpdateInput({
          id: customerId,
          data: { has_active_contracts: 0 },
          table: `${process.env.FINOPS_STAGE}-customers`,
        });
        const updateItemResponse = await databaseEntity.updateItem(
          inputUpdateItem
        );
        console.log("Cliente: ", customerId, "tem apenas contratos inativos");
        return await sendDataToUser(200, "success", updateItemResponse);
      }
    }
  } catch (error) {
    console.log(error);
    return await sendDataToUser(500, "error", error);
  }
};
